/* Responsive Design */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .hero-title {
        font-size: 4rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .docs-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Medium screens (768px to 1199px) */
@media (max-width: 1199px) {
    .hero-container {
        gap: var(--spacing-12);
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-6);
    }
    
    .docs-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        gap: var(--spacing-8);
    }
}

/* Tablet screens (768px to 991px) */
@media (max-width: 991px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-16);
        gap: var(--spacing-6);
        transition: var(--transition-normal);
        border-top: 1px solid var(--border-color);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-12);
    }
    
    .hero-image {
        order: -1;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }
    
    .feature-card {
        padding: var(--spacing-6);
    }
    
    .docs-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-4);
    }
    
    .download-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-4);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }
}

/* Mobile screens (576px to 767px) */
@media (max-width: 767px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .nav-container {
        padding: 0 var(--spacing-3);
    }
    
    .hero {
        padding: 100px 0 var(--spacing-16);
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-4);
    }
    
    .hero-description {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-6);
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .btn {
        padding: var(--spacing-3) var(--spacing-5);
        font-size: var(--font-size-sm);
    }
    
    .section-header h2 {
        font-size: var(--font-size-2xl);
    }
    
    .section-header p {
        font-size: var(--font-size-base);
    }
    
    .features,
    .documentation,
    .download {
        padding: var(--spacing-16) 0;
    }
    
    .features-grid {
        gap: var(--spacing-4);
    }
    
    .feature-card {
        padding: var(--spacing-5);
    }
    
    .feature-card h3 {
        font-size: var(--font-size-lg);
    }
    
    .docs-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .doc-card {
        padding: var(--spacing-5);
    }
    
    .download h2 {
        font-size: var(--font-size-2xl);
    }
    
    .download p {
        font-size: var(--font-size-base);
    }
    
    .download-btn {
        min-width: 180px;
        padding: var(--spacing-3) var(--spacing-4);
    }
    
    .download-btn i {
        font-size: var(--font-size-xl);
    }
    
    .download-btn strong {
        font-size: var(--font-size-base);
    }
    
    .qr-code img {
        width: 100px;
        height: 100px;
    }
    
    .footer {
        padding: var(--spacing-12) 0 var(--spacing-6);
    }
    
    .footer-content {
        gap: var(--spacing-6);
    }
}

/* Small mobile screens (up to 575px) */
@media (max-width: 575px) {
    .nav-logo span {
        display: none;
    }

    .nav-logo img {
        max-width: 100px;
    }
    
    .language-switcher {
        display: none;
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
        line-height: 1.3;
    }
    
    .hero-description {
        font-size: var(--font-size-sm);
    }
    
    .btn {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--font-size-xs);
    }
    
    .section-header h2 {
        font-size: var(--font-size-xl);
    }
    
    .section-header {
        margin-bottom: var(--spacing-12);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
    }
    
    .feature-icon i {
        font-size: var(--font-size-xl);
    }
    
    .feature-card h3 {
        font-size: var(--font-size-base);
    }
    
    .feature-card p {
        font-size: var(--font-size-sm);
    }
    
    .doc-icon {
        width: 40px;
        height: 40px;
    }
    
    .doc-icon i {
        font-size: var(--font-size-base);
    }
    
    .doc-card h3 {
        font-size: var(--font-size-base);
    }
    
    .doc-card p {
        font-size: var(--font-size-xs);
    }
    
    .download h2 {
        font-size: var(--font-size-xl);
    }
    
    .download-btn {
        min-width: 160px;
        padding: var(--spacing-2) var(--spacing-3);
    }
    
    .download-btn span {
        font-size: var(--font-size-xs);
    }
    
    .download-btn strong {
        font-size: var(--font-size-sm);
    }
    
    .qr-code {
        padding: var(--spacing-3);
    }
    
    .qr-code img {
        width: 80px;
        height: 80px;
    }
    
    .footer-logo span {
        display: none;
    }

    .footer-logo img {
        max-width: 100px;
    }
}

/* Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 90px 0 var(--spacing-12);
    }
    
    .hero-container {
        gap: var(--spacing-8);
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-3);
    }
    
    .hero-description {
        margin-bottom: var(--spacing-4);
    }
    
    .nav-menu {
        height: calc(100vh - 70px);
        padding-top: var(--spacing-8);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image img,
    .qr-code img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-title,
    .hero-description,
    .hero-buttons,
    .hero-image {
        animation: none;
    }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here in the future */
}
