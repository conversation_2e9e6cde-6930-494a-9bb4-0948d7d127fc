import 'package:sqflite/sqflite.dart';
import 'base_repository.dart';
import '../models/task.dart';
import '../services/database_helper.dart';

class TaskRepository extends BaseRepository<Task> {
  @override
  String get tableName => 'tasks';

  @override
  String get primaryKey => 'id';

  @override
  Task fromMap(Map<String, dynamic> map) {
    return Task.fromMap(map);
  }

  /// Get tasks by category
  Future<List<Task>> getTasksByCategory(String category) async {
    return await findWhere(
      'category = ?',
      [category],
      orderBy: 'is_completed ASC, priority DESC, due_date ASC',
    );
  }

  /// Get completed tasks
  Future<List<Task>> getCompletedTasks() async {
    return await findWhere(
      'is_completed = ?',
      [1],
      orderBy: 'updated_at DESC',
    );
  }

  /// Get pending tasks
  Future<List<Task>> getPendingTasks() async {
    return await findWhere(
      'is_completed = ?',
      [0],
      orderBy: 'priority DESC, due_date ASC',
    );
  }

  /// Get favorite tasks
  Future<List<Task>> getFavoriteTasks() async {
    return await findWhere(
      'is_favorite = ?',
      [1],
      orderBy: 'is_completed ASC, priority DESC, due_date ASC',
    );
  }

  /// Get tasks due today
  Future<List<Task>> getTasksDueToday() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return await findWhere(
      'due_date >= ? AND due_date < ? AND is_completed = ?',
      [startOfDay.toIso8601String(), endOfDay.toIso8601String(), 0],
      orderBy: 'priority DESC',
    );
  }

  /// Get overdue tasks
  Future<List<Task>> getOverdueTasks() async {
    final now = DateTime.now();

    return await findWhere(
      'due_date < ? AND is_completed = ?',
      [now.toIso8601String(), 0],
      orderBy: 'due_date ASC',
    );
  }

  /// Get tasks by priority
  Future<List<Task>> getTasksByPriority(TaskPriority priority) async {
    return await findWhere(
      'priority = ?',
      [priority.index],
      orderBy: 'is_completed ASC, due_date ASC',
    );
  }

  /// Search tasks by title or description
  Future<List<Task>> searchTasks(String query) async {
    return await findWhere(
      'title LIKE ? OR description LIKE ?',
      ['%$query%', '%$query%'],
      orderBy: 'is_completed ASC, priority DESC, due_date ASC',
    );
  }

  /// Get task completion rate
  Future<double> getCompletionRate() async {
    final totalCount = await count();
    if (totalCount == 0) return 0.0;

    final completedCount = await countWhere('is_completed = ?', [1]);
    return completedCount / totalCount;
  }

  /// Get tasks statistics by category
  Future<Map<String, Map<String, int>>> getTaskStatsByCategory() async {
    final result = await rawQuery('''
      SELECT 
        category,
        SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN is_completed = 0 THEN 1 ELSE 0 END) as pending,
        COUNT(*) as total
      FROM $tableName 
      GROUP BY category
    ''');

    final stats = <String, Map<String, int>>{};
    for (final row in result) {
      stats[row['category'] as String] = {
        'completed': row['completed'] as int,
        'pending': row['pending'] as int,
        'total': row['total'] as int,
      };
    }
    return stats;
  }

  /// Get tasks statistics by priority
  Future<Map<TaskPriority, int>> getTaskStatsByPriority() async {
    final result = await rawQuery('''
      SELECT priority, COUNT(*) as count
      FROM $tableName 
      WHERE is_completed = 0
      GROUP BY priority
    ''');

    final stats = <TaskPriority, int>{};
    for (final row in result) {
      final priority = TaskPriority.values[row['priority'] as int];
      stats[priority] = row['count'] as int;
    }
    return stats;
  }

  /// Mark task as completed
  Future<int> markAsCompleted(String taskId) async {
    final db = await _database;
    return await db.update(
      tableName,
      {
        'is_completed': 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  /// Mark task as pending
  Future<int> markAsPending(String taskId) async {
    final db = await _database;
    return await db.update(
      tableName,
      {
        'is_completed': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  /// Toggle favorite status
  Future<int> toggleFavorite(String taskId) async {
    final task = await findById(taskId);
    if (task == null) return 0;

    final db = await _database;
    return await db.update(
      tableName,
      {
        'is_favorite': task.isFavorite ? 0 : 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  /// Delete completed tasks older than specified days
  Future<int> deleteOldCompletedTasks(int daysOld) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

    final db = await _database;
    return await db.delete(
      tableName,
      where: 'is_completed = 1 AND updated_at < ?',
      whereArgs: [cutoffDate.toIso8601String()],
    );
  }

  Future<Database> get _database async => await DatabaseHelper().database;
}

class SubtaskRepository extends BaseRepository<Subtask> {
  @override
  String get tableName => 'subtasks';

  @override
  String get primaryKey => 'id';

  @override
  Subtask fromMap(Map<String, dynamic> map) {
    return Subtask.fromMap(map);
  }

  /// Get subtasks for a specific task
  Future<List<Subtask>> getSubtasksForTask(String taskId) async {
    return await findWhere(
      'task_id = ?',
      [taskId],
      orderBy: 'sort_order ASC, created_at ASC',
    );
  }

  /// Get completed subtasks for a task
  Future<List<Subtask>> getCompletedSubtasksForTask(String taskId) async {
    return await findWhere(
      'task_id = ? AND is_completed = ?',
      [taskId, 1],
      orderBy: 'sort_order ASC',
    );
  }

  /// Get pending subtasks for a task
  Future<List<Subtask>> getPendingSubtasksForTask(String taskId) async {
    return await findWhere(
      'task_id = ? AND is_completed = ?',
      [taskId, 0],
      orderBy: 'sort_order ASC',
    );
  }

  /// Mark subtask as completed
  Future<int> markAsCompleted(String subtaskId) async {
    final db = await _database;
    return await db.update(
      tableName,
      {
        'is_completed': 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [subtaskId],
    );
  }

  /// Mark subtask as pending
  Future<int> markAsPending(String subtaskId) async {
    final db = await _database;
    return await db.update(
      tableName,
      {
        'is_completed': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [subtaskId],
    );
  }

  /// Delete all subtasks for a task
  Future<int> deleteSubtasksForTask(String taskId) async {
    final db = await _database;
    return await db.delete(
      tableName,
      where: 'task_id = ?',
      whereArgs: [taskId],
    );
  }

  /// Update sort order for subtasks
  Future<void> updateSortOrder(List<String> subtaskIds) async {
    final db = await _database;
    final batch = db.batch();

    for (int i = 0; i < subtaskIds.length; i++) {
      batch.update(
        tableName,
        {
          'sort_order': i,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [subtaskIds[i]],
      );
    }

    await batch.commit();
  }

  Future<Database> get _database async => await DatabaseHelper().database;
}
