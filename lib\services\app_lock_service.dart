import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';
import 'package:crypto/crypto.dart';

class AppLockService extends ChangeNotifier {
  static AppLockService? _instance;
  static AppLockService get instance => _instance ??= AppLockService._();
  
  AppLockService._();

  // Private variables
  SharedPreferences? _prefs;
  final LocalAuthentication _localAuth = LocalAuthentication();
  
  bool _isInitialized = false;
  bool _isAppLockEnabled = false;
  String _lockMethod = 'pin'; // 'pin' or 'biometric'
  String? _hashedPin;
  bool _biometricAvailable = false;
  List<BiometricType> _availableBiometrics = [];
  bool _isLocked = false;
  DateTime? _lastUnlockTime;
  int _lockTimeoutMinutes = 5; // Auto-lock after 5 minutes

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isAppLockEnabled => _isAppLockEnabled;
  String get lockMethod => _lockMethod;
  bool get biometricAvailable => _biometricAvailable;
  List<BiometricType> get availableBiometrics => _availableBiometrics;
  bool get isLocked => _isLocked;
  int get lockTimeoutMinutes => _lockTimeoutMinutes;
  bool get hasPinSet => _hashedPin != null;

  /// Initialize the app lock service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('AppLockService: Starting initialization...');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _checkBiometricAvailability();
      
      _isInitialized = true;
      debugPrint('AppLockService: Initialization completed successfully');
      
      // Check if app should be locked on startup
      if (_isAppLockEnabled) {
        _checkAutoLock();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('AppLockService: Initialization failed: $e');
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    _isAppLockEnabled = _prefs?.getBool('app_lock_enabled') ?? false;
    _lockMethod = _prefs?.getString('lock_method') ?? 'pin';
    _hashedPin = _prefs?.getString('hashed_pin');
    _lockTimeoutMinutes = _prefs?.getInt('lock_timeout_minutes') ?? 5;
    
    final lastUnlockString = _prefs?.getString('last_unlock_time');
    if (lastUnlockString != null) {
      _lastUnlockTime = DateTime.tryParse(lastUnlockString);
    }
  }

  /// Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    await _prefs?.setBool('app_lock_enabled', _isAppLockEnabled);
    await _prefs?.setString('lock_method', _lockMethod);
    if (_hashedPin != null) {
      await _prefs?.setString('hashed_pin', _hashedPin!);
    }
    await _prefs?.setInt('lock_timeout_minutes', _lockTimeoutMinutes);
    
    if (_lastUnlockTime != null) {
      await _prefs?.setString('last_unlock_time', _lastUnlockTime!.toIso8601String());
    }
  }

  /// Check biometric availability
  Future<void> _checkBiometricAvailability() async {
    try {
      _biometricAvailable = await _localAuth.canCheckBiometrics;
      if (_biometricAvailable) {
        _availableBiometrics = await _localAuth.getAvailableBiometrics();
      }
      debugPrint('AppLockService: Biometric available: $_biometricAvailable');
      debugPrint('AppLockService: Available biometrics: $_availableBiometrics');
    } catch (e) {
      debugPrint('AppLockService: Error checking biometric availability: $e');
      _biometricAvailable = false;
      _availableBiometrics = [];
    }
  }

  /// Enable app lock
  Future<bool> enableAppLock(String method) async {
    try {
      if (method == 'biometric' && !_biometricAvailable) {
        return false;
      }

      _isAppLockEnabled = true;
      _lockMethod = method;
      await _saveSettings();
      
      notifyListeners();
      debugPrint('AppLockService: App lock enabled with method: $method');
      return true;
    } catch (e) {
      debugPrint('AppLockService: Error enabling app lock: $e');
      return false;
    }
  }

  /// Disable app lock
  Future<bool> disableAppLock() async {
    try {
      _isAppLockEnabled = false;
      _isLocked = false;
      _hashedPin = null;
      _lastUnlockTime = null;
      
      await _prefs?.remove('hashed_pin');
      await _prefs?.remove('last_unlock_time');
      await _saveSettings();
      
      notifyListeners();
      debugPrint('AppLockService: App lock disabled');
      return true;
    } catch (e) {
      debugPrint('AppLockService: Error disabling app lock: $e');
      return false;
    }
  }

  /// Set PIN for app lock
  Future<bool> setPIN(String pin) async {
    try {
      if (pin.length < 4 || pin.length > 6) {
        return false;
      }

      // Hash the PIN for security
      final bytes = utf8.encode(pin);
      final digest = sha256.convert(bytes);
      _hashedPin = digest.toString();
      
      await _saveSettings();
      notifyListeners();
      
      debugPrint('AppLockService: PIN set successfully');
      return true;
    } catch (e) {
      debugPrint('AppLockService: Error setting PIN: $e');
      return false;
    }
  }

  /// Verify PIN
  bool verifyPIN(String pin) {
    if (_hashedPin == null) return false;
    
    final bytes = utf8.encode(pin);
    final digest = sha256.convert(bytes);
    return digest.toString() == _hashedPin;
  }

  /// Authenticate with biometric
  Future<bool> authenticateWithBiometric() async {
    try {
      if (!_biometricAvailable) return false;

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to unlock FocusBro',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (isAuthenticated) {
        await _onSuccessfulUnlock();
      }

      return isAuthenticated;
    } catch (e) {
      debugPrint('AppLockService: Biometric authentication error: $e');
      return false;
    }
  }

  /// Unlock app with PIN
  Future<bool> unlockWithPIN(String pin) async {
    if (verifyPIN(pin)) {
      await _onSuccessfulUnlock();
      return true;
    }
    return false;
  }

  /// Handle successful unlock
  Future<void> _onSuccessfulUnlock() async {
    _isLocked = false;
    _lastUnlockTime = DateTime.now();
    await _saveSettings();
    notifyListeners();
    debugPrint('AppLockService: App unlocked successfully');
  }

  /// Lock the app
  void lockApp() {
    if (_isAppLockEnabled) {
      _isLocked = true;
      notifyListeners();
      debugPrint('AppLockService: App locked');
    }
  }

  /// Check if app should be auto-locked
  void _checkAutoLock() {
    if (!_isAppLockEnabled) return;

    if (_lastUnlockTime == null) {
      _isLocked = true;
      return;
    }

    final timeSinceUnlock = DateTime.now().difference(_lastUnlockTime!);
    if (timeSinceUnlock.inMinutes >= _lockTimeoutMinutes) {
      _isLocked = true;
    }
  }

  /// Set lock timeout
  Future<void> setLockTimeout(int minutes) async {
    _lockTimeoutMinutes = minutes;
    await _saveSettings();
    notifyListeners();
  }

  /// Check if app should be locked (call this on app resume)
  void checkLockStatus() {
    if (_isAppLockEnabled) {
      _checkAutoLock();
      notifyListeners();
    }
  }

  /// Get biometric type string
  String getBiometricTypeString() {
    if (_availableBiometrics.contains(BiometricType.face)) {
      return 'Face ID';
    } else if (_availableBiometrics.contains(BiometricType.fingerprint)) {
      return 'Fingerprint';
    } else if (_availableBiometrics.contains(BiometricType.iris)) {
      return 'Iris';
    } else {
      return 'Biometric';
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    debugPrint('AppLockService: Disposing...');
    super.dispose();
  }
}

/// App lock authentication result
class AppLockResult {
  final bool success;
  final String? error;
  final String? method;

  AppLockResult._({
    required this.success,
    this.error,
    this.method,
  });

  factory AppLockResult.success(String method) {
    return AppLockResult._(success: true, method: method);
  }

  factory AppLockResult.failure(String error) {
    return AppLockResult._(success: false, error: error);
  }
}
