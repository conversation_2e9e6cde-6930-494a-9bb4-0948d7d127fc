/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-dark: #111827;
    --border-color: #e5e7eb;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 600;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.nav-logo img {
    height: 32px;
    width: auto;
    max-width: 120px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

/* Enhanced Search Container */
.search-container {
    position: relative;
    margin-right: var(--spacing-4);
}

.search-input-wrapper {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 50px;
    padding: 0 var(--spacing-5);
    transition: var(--transition-normal);
    position: relative;
    min-width: 320px;
    height: 48px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 8px 30px rgba(99, 102, 241, 0.2);
    transform: translateY(-2px);
    background: white;
}

.search-icon {
    color: var(--text-light);
    margin-right: var(--spacing-3);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.search-input-wrapper:focus-within .search-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.search-input {
    border: none;
    outline: none;
    padding: 0;
    font-size: var(--font-size-sm);
    background: transparent;
    color: var(--text-primary);
    flex: 1;
    font-family: inherit;
    font-weight: 500;
}

.search-input::placeholder {
    color: var(--text-light);
    font-weight: 400;
}

.search-clear {
    background: var(--bg-secondary);
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;
    transition: var(--transition-fast);
    margin-left: var(--spacing-2);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
}

.search-clear:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.search-suggestions {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 420px;
    overflow-y: auto;
    display: none;
    backdrop-filter: blur(20px);
}

.search-suggestions.show {
    display: block;
    animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-suggestion {
    padding: var(--spacing-4) var(--spacing-5);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    position: relative;
}

.search-suggestion:first-child {
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.search-suggestion:last-child {
    border-bottom: none;
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.search-suggestion:hover,
.search-suggestion.highlighted {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: translateX(4px);
}

.search-suggestion:hover .search-suggestion-icon,
.search-suggestion.highlighted .search-suggestion-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.search-suggestion:hover .search-suggestion-title,
.search-suggestion.highlighted .search-suggestion-title {
    color: white;
}

.search-suggestion:hover .search-suggestion-description,
.search-suggestion.highlighted .search-suggestion-description {
    color: rgba(255, 255, 255, 0.9);
}

.search-suggestion:hover .search-suggestion-category,
.search-suggestion.highlighted .search-suggestion-category {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.search-suggestion-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-base);
    flex-shrink: 0;
    transition: var(--transition-normal);
}

.search-suggestion-content {
    flex: 1;
    min-width: 0;
}

.search-suggestion-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.search-suggestion-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    transition: var(--transition-fast);
}

.search-suggestion-category {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.search-no-results {
    padding: var(--spacing-8) var(--spacing-6);
    text-align: center;
    color: var(--text-secondary);
}

.search-no-results i {
    font-size: var(--font-size-3xl);
    color: var(--text-light);
    margin-bottom: var(--spacing-4);
    display: block;
}

.search-no-results div:first-of-type {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
}

.search-no-results div:last-of-type {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search Overlay for Mobile */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

.search-overlay.show {
    display: block;
}

/* Responsive Search */
@media (max-width: 768px) {
    .search-input-wrapper {
        min-width: 240px;
        height: 44px;
        padding: 0 var(--spacing-4);
    }

    .search-suggestions {
        left: -10px;
        right: -10px;
        max-height: 320px;
        border-radius: var(--radius-lg);
    }

    .search-suggestion {
        padding: var(--spacing-3) var(--spacing-4);
        gap: var(--spacing-3);
    }

    .search-suggestion-icon {
        width: 36px;
        height: 36px;
        font-size: var(--font-size-sm);
    }

    .search-suggestion-title {
        font-size: var(--font-size-sm);
    }

    .search-suggestion-description {
        font-size: var(--font-size-xs);
    }
}

@media (max-width: 480px) {
    .search-container {
        margin-right: var(--spacing-2);
    }

    .search-input-wrapper {
        min-width: 200px;
        height: 40px;
    }

    .search-input {
        font-size: var(--font-size-xs);
    }

    .search-suggestions {
        left: -20px;
        right: -20px;
    }
}

.language-switcher {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-1);
}

.lang-btn {
    background: none;
    border: none;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.lang-btn.active {
    background: var(--primary-color);
    color: white;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: 120px 0 var(--spacing-20);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-6);
    animation: fadeInUp 0.8s ease-out;
}

.hero-description {
    font-size: var(--font-size-lg);
    line-height: 1.7;
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-image {
    text-align: center;
    animation: fadeInRight 0.8s ease-out 0.6s both;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    cursor: pointer;
    border: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: var(--spacing-20) 0;
    background: var(--bg-secondary);
}



.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.feature-card {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
}

.feature-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.7;
}

/* Documentation Section */
.documentation {
    padding: var(--spacing-20) 0;
}

.docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
}

.doc-card {
    background: white;
    padding: var(--spacing-6);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    text-decoration: none;
    color: inherit;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
    display: block;
}

.doc-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.doc-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
}

.doc-icon i {
    font-size: var(--font-size-lg);
    color: white;
}

.doc-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--text-primary);
}

.doc-card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Download Section */
.download {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
}

.download::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.download-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.download h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.download p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.download-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.download-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-4) var(--spacing-6);
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    color: white;
    text-decoration: none;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    min-width: 200px;
}

.download-btn:hover:not(.disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.download-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.download-btn i {
    font-size: var(--font-size-2xl);
}

.download-btn div {
    text-align: left;
}

.download-btn span {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    display: block;
}

.download-btn strong {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.download-info-text {
    margin-bottom: var(--spacing-8);
}

.download-info-text p {
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-base);
}

.qr-code {
    display: inline-block;
    background: white;
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-medium);
}

.qr-code img {
    width: 120px;
    height: 120px;
    display: block;
    margin-bottom: var(--spacing-2);
}

.qr-code p {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: white;
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-8);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 600;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.footer-logo img {
    height: 32px;
    width: auto;
    max-width: 120px;
}

.footer-section p {
    color: #9ca3af;
    line-height: 1.7;
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-2);
}

.footer-section ul li a {
    color: #9ca3af;
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-6);
    text-align: center;
    color: #9ca3af;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
