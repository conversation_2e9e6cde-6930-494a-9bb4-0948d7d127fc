import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/app_lock_service.dart';

class AppLockScreen extends StatefulWidget {
  const AppLockScreen({super.key});

  @override
  State<AppLockScreen> createState() => _AppLockScreenState();
}

class _AppLockScreenState extends State<AppLockScreen>
    with TickerProviderStateMixin {
  final AppLockService _appLockService = AppLockService.instance;
  final TextEditingController _pinController = TextEditingController();
  
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;
  
  bool _isLoading = false;
  bool _obscurePin = true;
  String _enteredPin = '';
  int _failedAttempts = 0;
  static const int _maxFailedAttempts = 5;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tryBiometricAuth();
  }

  void _setupAnimations() {
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
  }

  Future<void> _tryBiometricAuth() async {
    if (_appLockService.lockMethod == 'biometric' && 
        _appLockService.biometricAvailable) {
      await Future.delayed(const Duration(milliseconds: 500));
      await _authenticateWithBiometric();
    }
  }

  Future<void> _authenticateWithBiometric() async {
    setState(() => _isLoading = true);
    
    try {
      final success = await _appLockService.authenticateWithBiometric();
      if (success && mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Biometric authentication failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _authenticateWithPin() async {
    if (_enteredPin.length < 4) return;
    
    setState(() => _isLoading = true);
    
    try {
      final success = await _appLockService.unlockWithPIN(_enteredPin);
      
      if (success && mounted) {
        Navigator.of(context).pop(true);
      } else {
        _handleFailedAttempt();
      }
    } catch (e) {
      _handleFailedAttempt();
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _handleFailedAttempt() {
    setState(() {
      _failedAttempts++;
      _enteredPin = '';
      _pinController.clear();
    });
    
    _shakeController.forward().then((_) {
      _shakeController.reverse();
    });
    
    HapticFeedback.vibrate();
    
    if (_failedAttempts >= _maxFailedAttempts) {
      _showTooManyAttemptsDialog();
    }
  }

  void _showTooManyAttemptsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Too Many Failed Attempts'),
        content: const Text(
          'You have exceeded the maximum number of failed attempts. '
          'Please try again later or contact support.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              SystemNavigator.pop();
            },
            child: const Text('Exit App'),
          ),
        ],
      ),
    );
  }

  void _onPinDigitPressed(String digit) {
    if (_enteredPin.length < 6) {
      setState(() {
        _enteredPin += digit;
        _pinController.text = _enteredPin;
      });
      
      if (_enteredPin.length >= 4) {
        _authenticateWithPin();
      }
    }
  }

  void _onDeletePressed() {
    if (_enteredPin.isNotEmpty) {
      setState(() {
        _enteredPin = _enteredPin.substring(0, _enteredPin.length - 1);
        _pinController.text = _enteredPin;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const Spacer(),
              
              // App Logo and Title
              Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.lock,
                      size: 40,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'FocusBro',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Enter your PIN to unlock',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 48),
              
              // PIN Input Display
              AnimatedBuilder(
                animation: _shakeAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(_shakeAnimation.value, 0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 16,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _failedAttempts > 0 
                            ? Colors.red 
                            : theme.colorScheme.outline,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(6, (index) {
                          final hasDigit = index < _enteredPin.length;
                          return Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: hasDigit 
                                ? theme.colorScheme.primary 
                                : Colors.transparent,
                              border: Border.all(
                                color: theme.colorScheme.outline,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          );
                        }),
                      ),
                    ),
                  );
                },
              ),
              
              if (_failedAttempts > 0) ...[
                const SizedBox(height: 16),
                Text(
                  'Incorrect PIN. ${_maxFailedAttempts - _failedAttempts} attempts remaining.',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              
              const SizedBox(height: 48),
              
              // PIN Keypad
              _buildPinKeypad(theme),
              
              const SizedBox(height: 32),
              
              // Biometric Button
              if (_appLockService.lockMethod == 'biometric' && 
                  _appLockService.biometricAvailable) ...[
                OutlinedButton.icon(
                  onPressed: _isLoading ? null : _authenticateWithBiometric,
                  icon: const Icon(Icons.fingerprint),
                  label: Text('Use ${_appLockService.getBiometricTypeString()}'),
                ),
              ],
              
              const Spacer(),
              
              // Loading Indicator
              if (_isLoading)
                const CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPinKeypad(ThemeData theme) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 300),
      child: Column(
        children: [
          // Rows 1-3
          for (int row = 0; row < 3; row++)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  for (int col = 1; col <= 3; col++)
                    _buildKeypadButton(
                      (row * 3 + col).toString(),
                      theme,
                    ),
                ],
              ),
            ),
          
          // Bottom row (0, delete)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                const SizedBox(width: 64), // Empty space
                _buildKeypadButton('0', theme),
                _buildDeleteButton(theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeypadButton(String digit, ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _isLoading ? null : () => _onPinDigitPressed(digit),
        borderRadius: BorderRadius.circular(32),
        child: Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(32),
          ),
          child: Center(
            child: Text(
              digit,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteButton(ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _isLoading ? null : _onDeletePressed,
        borderRadius: BorderRadius.circular(32),
        child: Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(32),
          ),
          child: const Center(
            child: Icon(Icons.backspace_outlined),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _pinController.dispose();
    super.dispose();
  }
}
