import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/cache_manager.dart';

/// Cloud synchronization service for FocusBro
class CloudSyncService {
  static final CloudSyncService _instance = CloudSyncService._internal();
  factory CloudSyncService() => _instance;
  CloudSyncService._internal();

  final CacheManager _cache = CacheManager();
  SharedPreferences? _prefs;

  // Sync status
  bool _isSyncing = false;
  bool _isOnline = true;
  DateTime? _lastSyncTime;
  String? _userId;

  // Sync configuration
  static const String _baseUrl = 'https://ymplpqzfeffvkgfhserm.supabase.co';
  static const String _apiKey =
      'your_supabase_anon_key'; // Replace with actual key

  // Sync intervals
  static const Duration _autoSyncInterval = Duration(minutes: 5);

  Timer? _autoSyncTimer;
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();

  /// Initialize cloud sync service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _userId = _prefs?.getString('user_id');
    _lastSyncTime = _getLastSyncTime();

    // Start auto-sync if user is logged in
    if (_userId != null) {
      _startAutoSync();
    }
  }

  /// Get sync status stream
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Check if user is authenticated
  bool get isAuthenticated => _userId != null;

  /// Get last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Get sync status
  bool get isSyncing => _isSyncing;

  /// Sign in user for cloud sync
  Future<bool> signIn(String email, String password) async {
    try {
      _updateSyncStatus(SyncStatus.authenticating);

      // Simulate authentication (replace with actual Supabase auth)
      await Future.delayed(const Duration(seconds: 2));

      // Generate user ID (in real implementation, get from auth response)
      _userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      await _prefs?.setString('user_id', _userId!);

      _updateSyncStatus(SyncStatus.authenticated);
      _startAutoSync();

      // Perform initial sync
      await syncAll();

      return true;
    } catch (error) {
      _updateSyncStatus(SyncStatus.error, error.toString());
      return false;
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    _stopAutoSync();
    _userId = null;
    await _prefs?.remove('user_id');
    await _prefs?.remove('last_sync_time');
    _updateSyncStatus(SyncStatus.signedOut);
  }

  /// Sync all data
  Future<bool> syncAll() async {
    if (!isAuthenticated || _isSyncing) return false;

    try {
      _isSyncing = true;
      _updateSyncStatus(SyncStatus.syncing);

      // Sync different data types
      await Future.wait([
        _syncTimerPresets(),
        _syncFocusSessions(),
        _syncSettings(),
        _syncNotes(),
        _syncTasks(),
      ]);

      _lastSyncTime = DateTime.now();
      await _saveLastSyncTime();

      _updateSyncStatus(SyncStatus.synced);
      return true;
    } catch (error) {
      _updateSyncStatus(SyncStatus.error, error.toString());
      return false;
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync timer presets
  Future<void> _syncTimerPresets() async {
    final localData = await _getLocalTimerPresets();
    final cloudData = await _getCloudTimerPresets();

    final mergedData = _mergeData(localData, cloudData, 'timer_presets');

    await _saveLocalTimerPresets(mergedData);
    await _saveCloudTimerPresets(mergedData);
  }

  /// Sync focus sessions
  Future<void> _syncFocusSessions() async {
    final localData = await _getLocalFocusSessions();
    final cloudData = await _getCloudFocusSessions();

    final mergedData = _mergeData(localData, cloudData, 'focus_sessions');

    await _saveLocalFocusSessions(mergedData);
    await _saveCloudFocusSessions(mergedData);
  }

  /// Sync user settings
  Future<void> _syncSettings() async {
    final localData = await _getLocalSettings();
    final cloudData = await _getCloudSettings();

    final mergedData = _mergeSettings(localData, cloudData);

    await _saveLocalSettings(mergedData);
    await _saveCloudSettings(mergedData);
  }

  /// Sync notes
  Future<void> _syncNotes() async {
    final localData = await _getLocalNotes();
    final cloudData = await _getCloudNotes();

    final mergedData = _mergeData(localData, cloudData, 'notes');

    await _saveLocalNotes(mergedData);
    await _saveCloudNotes(mergedData);
  }

  /// Sync tasks
  Future<void> _syncTasks() async {
    final localData = await _getLocalTasks();
    final cloudData = await _getCloudTasks();

    final mergedData = _mergeData(localData, cloudData, 'tasks');

    await _saveLocalTasks(mergedData);
    await _saveCloudTasks(mergedData);
  }

  /// Merge data with conflict resolution
  List<Map<String, dynamic>> _mergeData(
    List<Map<String, dynamic>> localData,
    List<Map<String, dynamic>> cloudData,
    String dataType,
  ) {
    final merged = <String, Map<String, dynamic>>{};

    // Add local data
    for (final item in localData) {
      final id = item['id'] ?? item['name'] ?? item['title'];
      if (id != null) {
        merged[id.toString()] = item;
      }
    }

    // Merge cloud data with conflict resolution
    for (final item in cloudData) {
      final id = item['id'] ?? item['name'] ?? item['title'];
      if (id != null) {
        final idStr = id.toString();

        if (merged.containsKey(idStr)) {
          // Conflict resolution: use most recent
          final localUpdated =
              DateTime.tryParse(merged[idStr]!['updatedAt'] ?? '');
          final cloudUpdated = DateTime.tryParse(item['updatedAt'] ?? '');

          if (cloudUpdated != null &&
              (localUpdated == null || cloudUpdated.isAfter(localUpdated))) {
            merged[idStr] = item;
          }
        } else {
          merged[idStr] = item;
        }
      }
    }

    return merged.values.toList();
  }

  /// Merge settings with priority rules
  Map<String, dynamic> _mergeSettings(
    Map<String, dynamic> localSettings,
    Map<String, dynamic> cloudSettings,
  ) {
    final merged = Map<String, dynamic>.from(localSettings);

    // Cloud settings take precedence for sync-enabled preferences
    final syncableSettings = [
      'workDuration',
      'breakDuration',
      'sessionsUntilLongBreak',
      'selectedNotificationSound',
      'isMusicEnabled',
      'selectedMusicTrack',
      'musicVolume',
    ];

    for (final key in syncableSettings) {
      if (cloudSettings.containsKey(key)) {
        merged[key] = cloudSettings[key];
      }
    }

    return merged;
  }

  /// Get local timer presets
  Future<List<Map<String, dynamic>>> _getLocalTimerPresets() async {
    _prefs ??= await SharedPreferences.getInstance();
    final savedTimersJson = _prefs!.getStringList('savedTimers') ?? [];
    return savedTimersJson
        .map((json) => jsonDecode(json) as Map<String, dynamic>)
        .toList();
  }

  /// Get cloud timer presets (mock implementation)
  Future<List<Map<String, dynamic>>> _getCloudTimerPresets() async {
    // In real implementation, make HTTP request to Supabase
    await Future.delayed(const Duration(milliseconds: 500));
    return []; // Return empty for now
  }

  /// Save local timer presets
  Future<void> _saveLocalTimerPresets(
      List<Map<String, dynamic>> presets) async {
    _prefs ??= await SharedPreferences.getInstance();
    final presetsJson = presets.map((preset) => jsonEncode(preset)).toList();
    await _prefs!.setStringList('savedTimers', presetsJson);

    // Invalidate cache
    _cache.invalidateCache(CacheManager.timerPresets);
  }

  /// Save cloud timer presets (mock implementation)
  Future<void> _saveCloudTimerPresets(
      List<Map<String, dynamic>> presets) async {
    // In real implementation, make HTTP request to Supabase
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Get local focus sessions
  Future<List<Map<String, dynamic>>> _getLocalFocusSessions() async {
    _prefs ??= await SharedPreferences.getInstance();
    final sessionsJson = _prefs!.getStringList('sessionHistory') ?? [];
    return sessionsJson
        .map((json) => jsonDecode(json) as Map<String, dynamic>)
        .toList();
  }

  /// Get cloud focus sessions (mock implementation)
  Future<List<Map<String, dynamic>>> _getCloudFocusSessions() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [];
  }

  /// Save local focus sessions
  Future<void> _saveLocalFocusSessions(
      List<Map<String, dynamic>> sessions) async {
    _prefs ??= await SharedPreferences.getInstance();
    final sessionsJson =
        sessions.map((session) => jsonEncode(session)).toList();
    await _prefs!.setStringList('sessionHistory', sessionsJson);

    // Invalidate cache
    _cache.invalidateCache(CacheManager.sessionHistory);
  }

  /// Save cloud focus sessions (mock implementation)
  Future<void> _saveCloudFocusSessions(
      List<Map<String, dynamic>> sessions) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Get local settings
  Future<Map<String, dynamic>> _getLocalSettings() async {
    _prefs ??= await SharedPreferences.getInstance();
    return {
      'workDuration': _prefs!.getInt('workDuration') ?? 1500,
      'breakDuration': _prefs!.getInt('breakDuration') ?? 300,
      'sessionsUntilLongBreak': _prefs!.getInt('sessionsUntilLongBreak') ?? 4,
      'selectedNotificationSound':
          _prefs!.getString('selectedNotificationSound') ?? 'default',
      'isMusicEnabled': _prefs!.getBool('isMusicEnabled') ?? false,
      'selectedMusicTrack': _prefs!.getString('selectedMusicTrack') ?? 'rain',
      'musicVolume': _prefs!.getDouble('musicVolume') ?? 0.5,
    };
  }

  /// Get cloud settings (mock implementation)
  Future<Map<String, dynamic>> _getCloudSettings() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {};
  }

  /// Save local settings
  Future<void> _saveLocalSettings(Map<String, dynamic> settings) async {
    _prefs ??= await SharedPreferences.getInstance();
    for (final entry in settings.entries) {
      final value = entry.value;
      if (value is int) {
        await _prefs!.setInt(entry.key, value);
      } else if (value is double) {
        await _prefs!.setDouble(entry.key, value);
      } else if (value is bool) {
        await _prefs!.setBool(entry.key, value);
      } else if (value is String) {
        await _prefs!.setString(entry.key, value);
      }
    }

    // Invalidate cache
    _cache.invalidateCache(CacheManager.userSettings);
  }

  /// Save cloud settings (mock implementation)
  Future<void> _saveCloudSettings(Map<String, dynamic> settings) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Get local notes (mock implementation)
  Future<List<Map<String, dynamic>>> _getLocalNotes() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return [];
  }

  /// Get cloud notes (mock implementation)
  Future<List<Map<String, dynamic>>> _getCloudNotes() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [];
  }

  /// Save local notes (mock implementation)
  Future<void> _saveLocalNotes(List<Map<String, dynamic>> notes) async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Save cloud notes (mock implementation)
  Future<void> _saveCloudNotes(List<Map<String, dynamic>> notes) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Get local tasks (mock implementation)
  Future<List<Map<String, dynamic>>> _getLocalTasks() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return [];
  }

  /// Get cloud tasks (mock implementation)
  Future<List<Map<String, dynamic>>> _getCloudTasks() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [];
  }

  /// Save local tasks (mock implementation)
  Future<void> _saveLocalTasks(List<Map<String, dynamic>> tasks) async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Save cloud tasks (mock implementation)
  Future<void> _saveCloudTasks(List<Map<String, dynamic>> tasks) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Start auto-sync timer
  void _startAutoSync() {
    _stopAutoSync();
    _autoSyncTimer = Timer.periodic(_autoSyncInterval, (timer) {
      if (!_isSyncing) {
        syncAll();
      }
    });
  }

  /// Stop auto-sync timer
  void _stopAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = null;
  }

  /// Update sync status
  void _updateSyncStatus(SyncStatus status, [String? message]) {
    _syncStatusController.add(status);
  }

  /// Get last sync time from preferences
  DateTime? _getLastSyncTime() {
    final timestamp = _prefs?.getInt('last_sync_time');
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  /// Save last sync time to preferences
  Future<void> _saveLastSyncTime() async {
    if (_lastSyncTime != null) {
      await _prefs?.setInt(
          'last_sync_time', _lastSyncTime!.millisecondsSinceEpoch);
    }
  }

  /// Force sync now
  Future<bool> forceSyncNow() async {
    return await syncAll();
  }

  /// Enable/disable auto sync
  Future<void> setAutoSyncEnabled(bool enabled) async {
    await _prefs?.setBool('auto_sync_enabled', enabled);
    if (enabled && isAuthenticated) {
      _startAutoSync();
    } else {
      _stopAutoSync();
    }
  }

  /// Check if auto sync is enabled
  bool get isAutoSyncEnabled => _prefs?.getBool('auto_sync_enabled') ?? true;

  /// Dispose resources
  void dispose() {
    _stopAutoSync();
    _syncStatusController.close();
  }
}

/// Sync status enumeration
enum SyncStatus {
  idle,
  authenticating,
  authenticated,
  syncing,
  synced,
  error,
  signedOut,
  offline,
}

/// Sync status extension for user-friendly messages
extension SyncStatusExtension on SyncStatus {
  String get message {
    switch (this) {
      case SyncStatus.idle:
        return 'Ready to sync';
      case SyncStatus.authenticating:
        return 'Signing in...';
      case SyncStatus.authenticated:
        return 'Signed in successfully';
      case SyncStatus.syncing:
        return 'Syncing data...';
      case SyncStatus.synced:
        return 'Data synced successfully';
      case SyncStatus.error:
        return 'Sync failed';
      case SyncStatus.signedOut:
        return 'Signed out';
      case SyncStatus.offline:
        return 'Offline - will sync when online';
    }
  }

  bool get isError => this == SyncStatus.error;
  bool get isSuccess =>
      this == SyncStatus.synced || this == SyncStatus.authenticated;
  bool get isLoading =>
      this == SyncStatus.authenticating || this == SyncStatus.syncing;
}
