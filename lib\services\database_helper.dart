import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  static const String _databaseName = 'focusbro.db';
  static const int _databaseVersion = 4;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _databaseName);

      if (kDebugMode) {
        print('Database path: $path');
      }

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: _onOpen,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing database: $e');
      }
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    if (kDebugMode) {
      print('Upgrading database from version $oldVersion to $newVersion');
    }

    // Handle specific version upgrades
    if (oldVersion < 3 && newVersion >= 3) {
      // Add sort_order column to tasks table
      try {
        await db.execute(
            'ALTER TABLE tasks ADD COLUMN sort_order INTEGER NOT NULL DEFAULT 0');
        await db
            .execute('CREATE INDEX idx_tasks_sort_order ON tasks(sort_order)');
        if (kDebugMode) {
          print(
              'DatabaseHelper: Successfully added sort_order column to tasks table');
        }
      } catch (e) {
        if (kDebugMode) {
          print('DatabaseHelper: Error adding sort_order column: $e');
        }
        // If column already exists or other error, continue
      }
    }

    if (oldVersion < 4 && newVersion >= 4) {
      // Add is_pinned and tags columns to notes table
      try {
        await db.execute(
            'ALTER TABLE notes ADD COLUMN is_pinned BOOLEAN NOT NULL DEFAULT 0');
        await db.execute('CREATE INDEX idx_notes_pinned ON notes(is_pinned)');
        if (kDebugMode) {
          print(
              'DatabaseHelper: Successfully added is_pinned column to notes table');
        }
      } catch (e) {
        if (kDebugMode) {
          print('DatabaseHelper: Error adding is_pinned column: $e');
        }
      }

      try {
        await db.execute('ALTER TABLE notes ADD COLUMN tags TEXT DEFAULT \'\'');
        await db.execute('CREATE INDEX idx_notes_tags ON notes(tags)');
        if (kDebugMode) {
          print(
              'DatabaseHelper: Successfully added tags column to notes table');
        }
      } catch (e) {
        if (kDebugMode) {
          print('DatabaseHelper: Error adding tags column: $e');
        }
      }
    }

    // For other major changes, recreate tables (in production, use proper migrations)
    if (oldVersion < 2) {
      await _dropAllTables(db);
      await _createTables(db);
    }
  }

  Future<void> _onOpen(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
  }

  Future<void> _createTables(Database db) async {
    // Create all tables
    await _createFocusSessionsTable(db);
    await _createTasksTable(db);
    await _createSubtasksTable(db);
    await _createNotesTable(db);
    await _createNoteAttachmentsTable(db);
    await _createAchievementsTable(db);
    await _createUserAchievementsTable(db);
    await _createRewardsTable(db);
    await _createSavedTimersTable(db);
    await _createUserSettingsTable(db);
    await _createFocusStatisticsTable(db);
    await _createTimerPresetsTable(db);

    // Insert default data
    await _insertDefaultData(db);
  }

  Future<void> _createFocusSessionsTable(Database db) async {
    await db.execute('''
      CREATE TABLE focus_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_date TEXT NOT NULL,
        work_duration INTEGER NOT NULL,
        break_duration INTEGER NOT NULL,
        total_duration INTEGER NOT NULL,
        session_type TEXT NOT NULL DEFAULT 'work',
        completed BOOLEAN NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute(
        'CREATE INDEX idx_focus_sessions_date ON focus_sessions(session_date)');
    await db.execute(
        'CREATE INDEX idx_focus_sessions_completed ON focus_sessions(completed)');
  }

  Future<void> _createTasksTable(Database db) async {
    await db.execute('''
      CREATE TABLE tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        priority INTEGER NOT NULL DEFAULT 1,
        due_date TEXT,
        is_completed BOOLEAN NOT NULL DEFAULT 0,
        is_favorite BOOLEAN NOT NULL DEFAULT 0,
        sort_order INTEGER NOT NULL DEFAULT 0,
        notification_enabled BOOLEAN NOT NULL DEFAULT 1,
        notification_reminder_minutes INTEGER NOT NULL DEFAULT 60,
        notification_repeat BOOLEAN NOT NULL DEFAULT 0,
        notification_repeat_interval INTEGER NOT NULL DEFAULT 0,
        notification_sound BOOLEAN NOT NULL DEFAULT 1,
        notification_vibration BOOLEAN NOT NULL DEFAULT 1,
        notification_priority INTEGER NOT NULL DEFAULT 2,
        notification_custom_sound TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX idx_tasks_category ON tasks(category)');
    await db.execute('CREATE INDEX idx_tasks_priority ON tasks(priority)');
    await db.execute('CREATE INDEX idx_tasks_due_date ON tasks(due_date)');
    await db.execute('CREATE INDEX idx_tasks_completed ON tasks(is_completed)');
    await db.execute('CREATE INDEX idx_tasks_favorite ON tasks(is_favorite)');
    await db.execute('CREATE INDEX idx_tasks_sort_order ON tasks(sort_order)');
  }

  Future<void> _createSubtasksTable(Database db) async {
    await db.execute('''
      CREATE TABLE subtasks (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL,
        title TEXT NOT NULL,
        is_completed BOOLEAN NOT NULL DEFAULT 0,
        sort_order INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('CREATE INDEX idx_subtasks_task_id ON subtasks(task_id)');
    await db.execute(
        'CREATE INDEX idx_subtasks_completed ON subtasks(is_completed)');
  }

  Future<void> _createNotesTable(Database db) async {
    await db.execute('''
      CREATE TABLE notes (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        category TEXT NOT NULL,
        is_favorite BOOLEAN NOT NULL DEFAULT 0,
        is_pinned BOOLEAN NOT NULL DEFAULT 0,
        tags TEXT DEFAULT '',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    await db.execute('CREATE INDEX idx_notes_category ON notes(category)');
    await db.execute('CREATE INDEX idx_notes_favorite ON notes(is_favorite)');
    await db.execute('CREATE INDEX idx_notes_pinned ON notes(is_pinned)');
    await db.execute('CREATE INDEX idx_notes_tags ON notes(tags)');
    await db.execute('CREATE INDEX idx_notes_created_at ON notes(created_at)');
  }

  Future<void> _createNoteAttachmentsTable(Database db) async {
    await db.execute('''
      CREATE TABLE note_attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_id TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_name TEXT NOT NULL,
        file_type TEXT,
        file_size INTEGER,
        created_at TEXT NOT NULL,
        FOREIGN KEY (note_id) REFERENCES notes (id) ON DELETE CASCADE
      )
    ''');

    await db.execute(
        'CREATE INDEX idx_note_attachments_note_id ON note_attachments(note_id)');
  }

  Future<void> _createAchievementsTable(Database db) async {
    await db.execute('''
      CREATE TABLE achievements (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        icon_code_point INTEGER NOT NULL,
        icon_font_family TEXT,
        achievement_type TEXT NOT NULL,
        target_value INTEGER,
        created_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createUserAchievementsTable(Database db) async {
    await db.execute('''
      CREATE TABLE user_achievements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        achievement_id TEXT NOT NULL,
        unlocked_at TEXT NOT NULL,
        progress INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (achievement_id) REFERENCES achievements (id) ON DELETE CASCADE
      )
    ''');

    await db.execute(
        'CREATE INDEX idx_user_achievements_achievement_id ON user_achievements(achievement_id)');
  }

  Future<void> _createRewardsTable(Database db) async {
    await db.execute('''
      CREATE TABLE rewards (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        required_streak INTEGER NOT NULL,
        is_unlocked BOOLEAN NOT NULL DEFAULT 0,
        unlocked_at TEXT,
        created_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createSavedTimersTable(Database db) async {
    await db.execute('''
      CREATE TABLE saved_timers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        work_duration INTEGER NOT NULL,
        break_duration INTEGER NOT NULL,
        sessions_until_long_break INTEGER NOT NULL DEFAULT 3,
        long_break_duration INTEGER,
        is_default BOOLEAN NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createUserSettingsTable(Database db) async {
    await db.execute('''
      CREATE TABLE user_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        data_type TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createFocusStatisticsTable(Database db) async {
    await db.execute('''
      CREATE TABLE focus_statistics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        current_streak INTEGER NOT NULL DEFAULT 0,
        longest_streak INTEGER NOT NULL DEFAULT 0,
        total_focus_time INTEGER NOT NULL DEFAULT 0,
        total_sessions INTEGER NOT NULL DEFAULT 0,
        last_focus_date TEXT,
        average_session_length INTEGER NOT NULL DEFAULT 0,
        best_day_date TEXT,
        best_day_duration INTEGER NOT NULL DEFAULT 0,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _createTimerPresetsTable(Database db) async {
    await db.execute('''
      CREATE TABLE timer_presets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        work_duration INTEGER NOT NULL,
        break_duration INTEGER NOT NULL,
        total_sessions INTEGER NOT NULL,
        is_built_in BOOLEAN NOT NULL DEFAULT 0,
        is_active BOOLEAN NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db
        .execute('CREATE INDEX idx_timer_presets_name ON timer_presets(name)');
    await db.execute(
        'CREATE INDEX idx_timer_presets_built_in ON timer_presets(is_built_in)');
    await db.execute(
        'CREATE INDEX idx_timer_presets_active ON timer_presets(is_active)');
  }

  Future<void> _insertDefaultData(Database db) async {
    // Insert default achievements
    await _insertDefaultAchievements(db);

    // Insert default rewards
    await _insertDefaultRewards(db);

    // Insert initial focus statistics
    await db.insert('focus_statistics', {
      'current_streak': 0,
      'longest_streak': 0,
      'total_focus_time': 0,
      'total_sessions': 0,
      'average_session_length': 0,
      'best_day_duration': 0,
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  Future<void> _insertDefaultAchievements(Database db) async {
    final achievements = [
      {
        'id': 'first_focus',
        'title': 'First Focus',
        'description': 'Complete your first focus session',
        'icon_code_point': 0xe3e7, // Icons.flag
        'achievement_type': 'session_count',
        'target_value': 1,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'streak_3',
        'title': 'Getting Started',
        'description': 'Maintain a 3-day streak',
        'icon_code_point': 0xe1e1, // Icons.local_fire_department
        'achievement_type': 'streak',
        'target_value': 3,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'streak_7',
        'title': 'On Fire',
        'description': 'Maintain a 7-day streak',
        'icon_code_point': 0xe5f1, // Icons.whatshot
        'achievement_type': 'streak',
        'target_value': 7,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'streak_30',
        'title': 'Focus Master',
        'description': 'Maintain a 30-day streak',
        'icon_code_point': 0xe6c4, // Icons.workspace_premium
        'achievement_type': 'streak',
        'target_value': 30,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'total_time_1h',
        'title': 'Time Builder',
        'description': 'Accumulate 1 hour of focus time',
        'icon_code_point': 0xe5d5, // Icons.timer
        'achievement_type': 'total_time',
        'target_value': 3600,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'total_time_10h',
        'title': 'Time Master',
        'description': 'Accumulate 10 hours of focus time',
        'icon_code_point': 0xe5d6, // Icons.timer_outlined
        'achievement_type': 'total_time',
        'target_value': 36000,
        'created_at': DateTime.now().toIso8601String(),
      },
    ];

    for (final achievement in achievements) {
      await db.insert('achievements', achievement);
    }
  }

  Future<void> _insertDefaultRewards(Database db) async {
    final rewards = [
      {
        'id': 'streak_3',
        'title': '3-Day Streak',
        'description': 'Unlock custom timer presets',
        'required_streak': 3,
        'is_unlocked': 0,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'streak_7',
        'title': '7-Day Streak',
        'description': 'Unlock premium focus sounds',
        'required_streak': 7,
        'is_unlocked': 0,
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'streak_30',
        'title': '30-Day Streak',
        'description': 'Unlock all premium features',
        'required_streak': 30,
        'is_unlocked': 0,
        'created_at': DateTime.now().toIso8601String(),
      },
    ];

    for (final reward in rewards) {
      await db.insert('rewards', reward);
    }
  }

  Future<void> _dropAllTables(Database db) async {
    final tables = [
      'focus_sessions',
      'tasks',
      'subtasks',
      'notes',
      'note_attachments',
      'achievements',
      'user_achievements',
      'rewards',
      'saved_timers',
      'user_settings',
      'focus_statistics',
      'timer_presets',
    ];

    for (final table in tables) {
      await db.execute('DROP TABLE IF EXISTS $table');
    }
  }

  /// Close the database
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  /// Delete the database file (for testing purposes)
  Future<void> deleteDatabase() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _databaseName);
      await File(path).delete();
      _database = null;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting database: $e');
      }
    }
  }

  // Backup & Restore operations

  /// Get all tasks for backup
  Future<List<Map<String, dynamic>>> getAllTasks() async {
    final db = await database;
    return await db.query('tasks');
  }

  /// Get all notes for backup
  Future<List<Map<String, dynamic>>> getAllNotes() async {
    final db = await database;
    return await db.query('notes');
  }

  /// Get all focus sessions for backup
  Future<List<Map<String, dynamic>>> getAllFocusSessions() async {
    final db = await database;
    return await db.query('focus_sessions');
  }

  /// Get all timer presets for backup
  Future<List<Map<String, dynamic>>> getAllTimerPresets() async {
    final db = await database;
    return await db.query('timer_presets');
  }

  /// Get all subtasks for backup
  Future<List<Map<String, dynamic>>> getAllSubtasks() async {
    final db = await database;
    return await db.query('subtasks');
  }

  /// Get all achievements for backup
  Future<List<Map<String, dynamic>>> getAllAchievements() async {
    final db = await database;
    return await db.query('achievements');
  }

  /// Get all user achievements for backup
  Future<List<Map<String, dynamic>>> getAllUserAchievements() async {
    final db = await database;
    return await db.query('user_achievements');
  }

  /// Get all rewards for backup
  Future<List<Map<String, dynamic>>> getAllRewards() async {
    final db = await database;
    return await db.query('rewards');
  }

  /// Get all saved timers for backup
  Future<List<Map<String, dynamic>>> getAllSavedTimers() async {
    final db = await database;
    return await db.query('saved_timers');
  }

  /// Get all user settings for backup
  Future<List<Map<String, dynamic>>> getAllUserSettings() async {
    final db = await database;
    return await db.query('user_settings');
  }

  /// Get focus statistics for backup
  Future<List<Map<String, dynamic>>> getFocusStatistics() async {
    final db = await database;
    return await db.query('focus_statistics');
  }

  /// Clear all tasks
  Future<void> clearAllTasks() async {
    final db = await database;
    await db.delete('tasks');
  }

  /// Clear all notes
  Future<void> clearAllNotes() async {
    final db = await database;
    await db.delete('notes');
  }

  /// Clear all focus sessions
  Future<void> clearAllFocusSessions() async {
    final db = await database;
    await db.delete('focus_sessions');
  }

  /// Clear all timer presets (except built-in ones)
  Future<void> clearAllTimerPresets() async {
    final db = await database;
    await db.delete('timer_presets', where: 'is_built_in = ?', whereArgs: [0]);
  }

  /// Clear all subtasks
  Future<void> clearAllSubtasks() async {
    final db = await database;
    await db.delete('subtasks');
  }

  /// Clear all user achievements
  Future<void> clearAllUserAchievements() async {
    final db = await database;
    await db.delete('user_achievements');
  }

  /// Clear all rewards
  Future<void> clearAllRewards() async {
    final db = await database;
    await db.delete('rewards');
  }

  /// Clear all saved timers
  Future<void> clearAllSavedTimers() async {
    final db = await database;
    await db.delete('saved_timers');
  }

  /// Clear all user settings
  Future<void> clearAllUserSettings() async {
    final db = await database;
    await db.delete('user_settings');
  }

  /// Reset focus statistics
  Future<void> resetFocusStatistics() async {
    final db = await database;
    await db.delete('focus_statistics');
    // Insert default statistics
    await db.insert('focus_statistics', {
      'current_streak': 0,
      'longest_streak': 0,
      'total_focus_time': 0,
      'total_sessions': 0,
      'average_session_length': 0,
      'best_day_duration': 0,
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  /// Insert task for restore
  Future<int> insertTask(Map<String, dynamic> task) async {
    final db = await database;
    return await db.insert('tasks', task,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert note for restore
  Future<int> insertNote(Map<String, dynamic> note) async {
    final db = await database;
    return await db.insert('notes', note,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert focus session for restore
  Future<int> insertFocusSession(Map<String, dynamic> session) async {
    final db = await database;
    return await db.insert('focus_sessions', session,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert timer preset for restore
  Future<int> insertTimerPreset(Map<String, dynamic> preset) async {
    final db = await database;
    return await db.insert('timer_presets', preset,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert subtask for restore
  Future<int> insertSubtask(Map<String, dynamic> subtask) async {
    final db = await database;
    return await db.insert('subtasks', subtask,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert user achievement for restore
  Future<int> insertUserAchievement(Map<String, dynamic> achievement) async {
    final db = await database;
    return await db.insert('user_achievements', achievement,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert reward for restore
  Future<int> insertReward(Map<String, dynamic> reward) async {
    final db = await database;
    return await db.insert('rewards', reward,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert saved timer for restore
  Future<int> insertSavedTimer(Map<String, dynamic> timer) async {
    final db = await database;
    return await db.insert('saved_timers', timer,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert user setting for restore
  Future<int> insertUserSetting(Map<String, dynamic> setting) async {
    final db = await database;
    return await db.insert('user_settings', setting,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Insert focus statistics for restore
  Future<int> insertFocusStatistics(Map<String, dynamic> stats) async {
    final db = await database;
    return await db.insert('focus_statistics', stats,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }
}
