<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-en="Language Test - FocusBro" data-id="Tes Bahasa - FocusBro">Language Test - FocusBro</title>
    <meta name="description" content="Test language switching functionality" data-en="Test language switching functionality" data-id="Tes fungsi pergantian bahasa">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-main {
            padding-top: 70px;
            min-height: 100vh;
            background: var(--bg-secondary);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-8) var(--spacing-4);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: var(--spacing-8);
            padding: var(--spacing-6);
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-light);
        }
        
        .test-section {
            background: white;
            padding: var(--spacing-6);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-6);
            box-shadow: var(--shadow-light);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-3);
            border-bottom: 1px solid var(--border-color);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-label {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .test-value {
            color: var(--text-secondary);
            font-family: monospace;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: var(--spacing-2);
        }
        
        .status-indicator.success {
            background: #10b981;
        }
        
        .status-indicator.error {
            background: #ef4444;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-2);
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-bottom: var(--spacing-6);
            transition: var(--transition-fast);
        }
        
        .back-link:hover {
            color: var(--primary-dark);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="assets/images/focusbro-logo.png" alt="FocusBro Logo">
                <span>FocusBro</span>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link" data-en="Home" data-id="Beranda">Home</a>
                <a href="index.html#features" class="nav-link" data-en="Features" data-id="Fitur">Features</a>
                <a href="index.html#docs" class="nav-link" data-en="Documentation" data-id="Dokumentasi">Documentation</a>
                <a href="index.html#download" class="nav-link" data-en="Download" data-id="Unduh">Download</a>
            </div>
            
            <div class="nav-actions">
                <div class="search-container" id="searchContainer">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="site-search" placeholder="Search documentation..." class="search-input" autocomplete="off">
                        <button class="search-clear" id="searchClear" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="search-suggestions" id="searchSuggestions"></div>
                </div>
                <div class="language-switcher">
                    <button class="lang-btn active" data-lang="en">EN</button>
                    <button class="lang-btn" data-lang="id">ID</button>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Test Content -->
    <main class="test-main">
        <div class="test-container">
            <a href="index.html" class="back-link">
                <i class="fas fa-arrow-left"></i> <span data-en="Back to Home" data-id="Kembali ke Beranda">Back to Home</span>
            </a>
            
            <div class="test-header">
                <h1 data-en="Language Toggle Test" data-id="Tes Toggle Bahasa">Language Toggle Test</h1>
                <p data-en="Test the language switching functionality across different elements" data-id="Tes fungsi pergantian bahasa di berbagai elemen">Test the language switching functionality across different elements</p>
            </div>

            <div class="test-section">
                <h2 data-en="Navigation Elements" data-id="Elemen Navigasi">Navigation Elements</h2>
                <div class="test-item">
                    <span class="test-label" data-en="Home Link" data-id="Link Beranda">Home Link</span>
                    <span class="test-value" id="nav-home-test">Home</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Features Link" data-id="Link Fitur">Features Link</span>
                    <span class="test-value" id="nav-features-test">Features</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Documentation Link" data-id="Link Dokumentasi">Documentation Link</span>
                    <span class="test-value" id="nav-docs-test">Documentation</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Download Link" data-id="Link Unduh">Download Link</span>
                    <span class="test-value" id="nav-download-test">Download</span>
                    <div class="status-indicator success"></div>
                </div>
            </div>

            <div class="test-section">
                <h2 data-en="Content Elements" data-id="Elemen Konten">Content Elements</h2>
                <div class="test-item">
                    <span class="test-label" data-en="Page Title" data-id="Judul Halaman">Page Title</span>
                    <span class="test-value" id="page-title-test">Language Test - FocusBro</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Back Link" data-id="Link Kembali">Back Link</span>
                    <span class="test-value" id="back-link-test">Back to Home</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Search Placeholder" data-id="Placeholder Pencarian">Search Placeholder</span>
                    <span class="test-value" id="search-placeholder-test">Search documentation...</span>
                    <div class="status-indicator success"></div>
                </div>
            </div>

            <div class="test-section">
                <h2 data-en="Test Results" data-id="Hasil Tes">Test Results</h2>
                <div class="test-item">
                    <span class="test-label" data-en="Current Language" data-id="Bahasa Saat Ini">Current Language</span>
                    <span class="test-value" id="current-lang">English</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Elements Found" data-id="Elemen Ditemukan">Elements Found</span>
                    <span class="test-value" id="elements-count">0</span>
                    <div class="status-indicator success"></div>
                </div>
                <div class="test-item">
                    <span class="test-label" data-en="Language Buttons" data-id="Tombol Bahasa">Language Buttons</span>
                    <span class="test-value" id="lang-buttons-count">0</span>
                    <div class="status-indicator success"></div>
                </div>
            </div>

            <div class="test-section">
                <h2 data-en="Sample Text" data-id="Contoh Teks">Sample Text</h2>
                <p data-en="This is a sample paragraph to test language switching. When you click the language toggle, this text should change to Indonesian." data-id="Ini adalah paragraf contoh untuk menguji pergantian bahasa. Ketika Anda mengklik toggle bahasa, teks ini harus berubah ke bahasa Indonesia.">This is a sample paragraph to test language switching. When you click the language toggle, this text should change to Indonesian.</p>
                
                <ul>
                    <li data-en="First test item" data-id="Item tes pertama">First test item</li>
                    <li data-en="Second test item" data-id="Item tes kedua">Second test item</li>
                    <li data-en="Third test item" data-id="Item tes ketiga">Third test item</li>
                </ul>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update test values
            function updateTestValues() {
                const elementsWithDataEn = document.querySelectorAll('[data-en]');
                const langButtons = document.querySelectorAll('.lang-btn');
                const currentLang = document.documentElement.lang || 'en';
                
                document.getElementById('elements-count').textContent = elementsWithDataEn.length;
                document.getElementById('lang-buttons-count').textContent = langButtons.length;
                document.getElementById('current-lang').textContent = currentLang === 'en' ? 'English' : 'Indonesian';
                
                // Update page title test
                const titleElement = document.querySelector('title');
                if (titleElement) {
                    document.getElementById('page-title-test').textContent = titleElement.textContent;
                }
                
                // Update search placeholder test
                const searchInput = document.getElementById('site-search');
                if (searchInput) {
                    document.getElementById('search-placeholder-test').textContent = searchInput.placeholder;
                }
            }
            
            // Listen for language changes
            window.addEventListener('languageChanged', function(e) {
                console.log('Language changed to:', e.detail.language);
                setTimeout(updateTestValues, 100);
            });
            
            // Initial update
            updateTestValues();
            
            // Update every 2 seconds for testing
            setInterval(updateTestValues, 2000);
        });
    </script>
</body>
</html>
